/**
 * @file Governance-Tracking Bridge Service Unit Tests
 * @filepath server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts
 * @task-id I-TSK-01.SUB-01.1.IMP-01
 * @component governance-tracking-bridge-tests
 * @reference foundation-context.INTEGRATION.001
 * @tier T1
 * @context foundation-context
 * @category Integration-Bridge-Testing
 * @created 2025-01-09 14:00:00 +03
 * @modified 2025-01-09 14:00:00 +03
 *
 * @description
 * Comprehensive unit tests for Governance-Tracking Bridge Service:
 * - Bridge initialization and configuration testing
 * - Governance rules synchronization validation
 * - Cross-system compliance testing
 * - Event handling and processing verification
 * - Health monitoring and diagnostics testing
 * - Performance validation and resilient timing integration
 * - Memory safety and resource cleanup verification
 * - Error handling and recovery mechanism testing
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level integration-bridge-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-013-integration-bridge-testing
 * @governance-dcr DCR-foundation-013-integration-bridge-testing
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🎯 TESTING STANDARDS (v2.1)
 * @coverage-target ≥95% (statements, branches, functions, lines)
 * @test-categories unit, integration, performance, memory-safety
 * @anti-simplification-compliant true
 * @memory-safe-testing true
 * @resilient-timing-validation true
 */

import { GovernanceTrackingBridge } from '../GovernanceTrackingBridge';
import {
  TBridgeConfig,
  TGovernanceSystemConfig,
  TTrackingSystemConfig,
  TGovernanceEvent,
  TTrackingEvent,
  TValidationScope
} from '../../../../../../shared/src/types/platform/governance/governance-types';
import {
  TGovernanceRule
} from '../../../../../../shared/src/types/platform/governance/rule-management-types';
import {
  TTrackingData,
  TValidationResult
} from '../../../../../../shared/src/types/platform/tracking/tracking-types';

// ============================================================================
// MOCK CONFIGURATIONS AND TEST DATA
// ============================================================================

// Mock external dependencies to prevent hanging and ensure fast test execution
jest.mock('../../../../../../shared/src/base/timer-coordination/modules/TimerConfiguration', () => ({
  createResilientTimer: jest.fn(() => ({
    start: jest.fn(() => ({
      end: jest.fn(() => ({ duration: 10, timestamp: new Date() }))
    }))
  })),
  createResilientMetricsCollector: jest.fn(() => ({
    recordTiming: jest.fn(),
    getMetrics: jest.fn(() => ({}))
  }))
}));

// Mock BaseTrackingService to prevent complex initialization
jest.mock('../../../tracking/core-data/base/BaseTrackingService', () => {
  return {
    BaseTrackingService: class MockBaseTrackingService {
      protected _config: any;
      private _initialized = false;
      private _ready = false;

      constructor(config: any) {
        this._config = config;
      }

      async initialize(): Promise<void> {
        this._initialized = true;
        this._ready = true;
        await this.doInitialize();
      }

      async shutdown(): Promise<void> {
        this._ready = false;
        await this.doShutdown();
      }

      isReady(): boolean {
        return this._ready;
      }

      generateId(): string {
        return `test-id-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      }

      logInfo(message: string, data?: any): void {
        // Silent logging for tests
      }

      logError(message: string, data?: any): void {
        // Silent logging for tests
      }

      createSafeInterval(callback: () => void, interval: number, name: string): void {
        // Mock safe interval creation
      }

      createSafeTimeout(callback: () => void, timeout: number, name: string): void {
        // Mock safe timeout creation
      }

      protected async doInitialize(): Promise<void> {
        // Override in subclass
      }

      protected async doShutdown(): Promise<void> {
        // Override in subclass
      }

      protected getServiceName(): string {
        return 'MockService';
      }

      protected getServiceVersion(): string {
        return '1.0.0';
      }

      protected async doTrack(data: any): Promise<void> {
        // Mock implementation
      }

      protected async doValidate(): Promise<TValidationResult> {
        return {
          validationId: this.generateId(),
          componentId: this.getServiceName(),
          timestamp: new Date(),
          executionTime: 0,
          status: 'valid',
          overallScore: 100,
          checks: [],
          references: {
            componentId: this.getServiceName(),
            internalReferences: [],
            externalReferences: [],
            circularReferences: [],
            missingReferences: [],
            redundantReferences: [],
            metadata: {
              totalReferences: 0,
              buildTimestamp: new Date(),
              analysisDepth: 1
            }
          },
          recommendations: [],
          warnings: [],
          errors: [],
          metadata: {
            validationMethod: 'mock-validation',
            rulesApplied: 1,
            dependencyDepth: 0,
            cyclicDependencies: [],
            orphanReferences: []
          }
        };
      }
    }
  };
});

// ============================================================================
// TEST DATA FACTORIES
// ============================================================================

const createTestBridgeConfig = (): TBridgeConfig => ({
  bridgeId: 'test-bridge-001',
  bridgeName: 'Test Governance-Tracking Bridge',
  governanceSystem: {
    systemId: 'governance-system-001',
    systemName: 'Test Governance System',
    version: '1.0.0',
    endpoints: [{
      endpointId: 'gov-endpoint-001',
      name: 'governance-api',
      url: 'http://localhost:3001/api/governance',
      method: 'POST',
      authentication: true,
      timeout: 5000,
      retryPolicy: {
        maxAttempts: 3,
        initialDelay: 1000,
        backoffMultiplier: 2,
        maxDelay: 10000,
        retryableErrors: ['timeout', 'connection']
      },
      metadata: {}
    }],
    authentication: {
      type: 'bearer',
      credentials: { token: 'test-token' },
      metadata: {}
    },
    rulesSyncInterval: 60000,
    complianceCheckInterval: 30000,
    eventSubscriptions: ['rule-change', 'compliance-update'],
    metadata: {}
  } as TGovernanceSystemConfig,
  trackingSystem: {
    systemId: 'tracking-system-001',
    systemName: 'Test Tracking System',
    version: '1.0.0',
    endpoints: [{
      endpointId: 'track-endpoint-001',
      name: 'tracking-api',
      url: 'http://localhost:3002/api/tracking',
      method: 'POST',
      authentication: true,
      timeout: 5000,
      retryPolicy: {
        maxAttempts: 3,
        initialDelay: 1000,
        backoffMultiplier: 2,
        maxDelay: 10000,
        retryableErrors: ['timeout', 'connection']
      },
      metadata: {}
    }],
    authentication: {
      type: 'bearer',
      credentials: { token: 'test-token' },
      metadata: {}
    },
    dataSyncInterval: 30000,
    metricsCollectionInterval: 10000,
    eventSubscriptions: ['data-update', 'metrics-change'],
    metadata: {}
  } as TTrackingSystemConfig,
  synchronizationSettings: {
    enabled: true,
    interval: 60000,
    batchSize: 100,
    retryPolicy: {
      maxAttempts: 3,
      initialDelay: 1000,
      backoffMultiplier: 2,
      maxDelay: 10000,
      retryableErrors: ['timeout', 'connection']
    },
    conflictResolution: 'governance-wins',
    metadata: {}
  },
  eventHandlingSettings: {
    enabled: true,
    eventTypes: ['governance-rule-change', 'tracking-data-update'],
    processingMode: 'async',
    bufferSize: 1000,
    timeout: 5000,
    retryPolicy: {
      maxAttempts: 3,
      initialDelay: 1000,
      backoffMultiplier: 2,
      maxDelay: 10000,
      retryableErrors: ['timeout', 'connection']
    },
    metadata: {}
  },
  healthCheckSettings: {
    enabled: true,
    interval: 30000,
    timeout: 5000,
    thresholds: {
      latency: 1000,
      errorRate: 0.1,
      throughput: 100,
      uptime: 0.99,
      memoryUsage: 100,
      cpuUsage: 80
    },
    alerting: {
      enabled: true,
      channels: ['email', 'slack'],
      severity: 'medium',
      escalation: {
        enabled: true,
        levels: [{
          level: 1,
          delay: 300000,
          channels: ['email'],
          recipients: ['<EMAIL>'],
          metadata: {}
        }],
        timeout: 3600000,
        metadata: {}
      },
      metadata: {}
    },
    metadata: {}
  },
  diagnosticsSettings: {
    enabled: true,
    level: 'comprehensive',
    retentionPeriod: 604800000, // 7 days
    exportEnabled: true,
    metadata: {}
  },
  metadata: {}
});

const createTestGovernanceRule = (): TGovernanceRule => ({
  ruleId: 'test-rule-001',
  name: 'Test Governance Rule',
  type: 'validation',
  description: 'Test rule for bridge validation',
  version: '1.0.0',
  status: 'active',
  priority: 1,
  category: 'compliance',
  scope: ['tracking-system'],
  conditions: [{
    conditionId: 'condition-001',
    field: 'status',
    operator: 'equals',
    value: 'active',
    metadata: {}
  }],
  actions: [{
    actionId: 'action-001',
    type: 'validate',
    parameters: { strict: true },
    metadata: {}
  }],
  metadata: {
    createdBy: 'test-user',
    createdAt: new Date(),
    authority: 'integration-bridge-authority'
  }
});

const createTestTrackingData = (): TTrackingData => ({
  componentId: 'test-component-001',
  status: 'in-progress',
  timestamp: new Date().toISOString(),
  metadata: {
    phase: 'testing',
    progress: 50,
    priority: 'P1',
    tags: ['test', 'bridge'],
    custom: { testProperty: 'testValue' }
  },
  context: {
    contextId: 'foundation-context',
    milestone: 'M0',
    category: 'integration',
    dependencies: [],
    dependents: []
  },
  progress: {
    completion: 50,
    tasksCompleted: 5,
    totalTasks: 10,
    timeSpent: 120,
    estimatedTimeRemaining: 120,
    quality: {
      codeCoverage: 85,
      testCount: 15,
      bugCount: 0,
      qualityScore: 90,
      performanceScore: 95
    }
  },
  authority: {
    validator: 'integration-bridge-authority',
    level: 'architectural-authority',
    complianceScore: 95,
    lastValidated: new Date(),
    metadata: {}
  }
});

// ============================================================================
// MAIN TEST SUITE
// ============================================================================

describe('GovernanceTrackingBridge Unit Tests', () => {
  let bridge: GovernanceTrackingBridge;
  let testBridgeConfig: TBridgeConfig;
  let testGovernanceRule: TGovernanceRule;
  let testTrackingData: TTrackingData;

  beforeAll(() => {
    // Set test environment
    process.env.NODE_ENV = 'test';
    process.env.JEST_WORKER_ID = process.env.JEST_WORKER_ID || '1';
    
    // Set test timeout
    jest.setTimeout(10000);
  });

  beforeEach(() => {
    jest.clearAllMocks();
    bridge = new GovernanceTrackingBridge();
    testBridgeConfig = createTestBridgeConfig();
    testGovernanceRule = createTestGovernanceRule();
    testTrackingData = createTestTrackingData();
  });

  afterEach(async () => {
    if (bridge && bridge.isReady()) {
      await bridge.shutdown();
    }
  });

  // ============================================================================
  // SERVICE LIFECYCLE TESTS
  // ============================================================================

  describe('Service Lifecycle Management', () => {
    test('should create bridge service instance successfully', () => {
      expect(bridge).toBeDefined();
      expect(bridge).toBeInstanceOf(GovernanceTrackingBridge);
      expect(bridge.isReady()).toBe(false);
    });

    test('should initialize bridge service successfully', async () => {
      await bridge.initialize();
      expect(bridge.isReady()).toBe(true);
    });

    test('should shutdown bridge service successfully', async () => {
      await bridge.initialize();
      expect(bridge.isReady()).toBe(true);
      
      await bridge.shutdown();
      expect(bridge.isReady()).toBe(false);
    });

    test('should handle double initialization gracefully', async () => {
      await bridge.initialize();
      expect(bridge.isReady()).toBe(true);
      
      await expect(bridge.initialize()).resolves.not.toThrow();
      expect(bridge.isReady()).toBe(true);
    });
  });

  // ============================================================================
  // BRIDGE INITIALIZATION TESTS
  // ============================================================================

  describe('Bridge Initialization', () => {
    beforeEach(async () => {
      await bridge.initialize();
    });

    test('should initialize bridge with valid configuration', async () => {
      const result = await bridge.initializeBridge(testBridgeConfig);

      expect(result.success).toBe(true);
      expect(result.bridgeId).toBe(testBridgeConfig.bridgeId);
      expect(result.governanceConnection).toBeDefined();
      expect(result.trackingConnection).toBeDefined();
      expect(result.errors).toHaveLength(0);
    });

    test('should reject initialization with invalid configuration', async () => {
      const invalidConfig = { ...testBridgeConfig, bridgeId: '' };

      const result = await bridge.initializeBridge(invalidConfig);

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should handle bridge initialization timeout gracefully', async () => {
      // Mock timeout scenario
      const timeoutConfig = {
        ...testBridgeConfig,
        governanceSystem: {
          ...testBridgeConfig.governanceSystem,
          endpoints: [{
            ...testBridgeConfig.governanceSystem.endpoints[0],
            timeout: 1 // Very short timeout
          }]
        }
      };

      const result = await bridge.initializeBridge(timeoutConfig);

      // Should handle timeout gracefully
      expect(result).toBeDefined();
      expect(result.bridgeId).toBe(timeoutConfig.bridgeId);
    });
  });

  // ============================================================================
  // GOVERNANCE RULES SYNCHRONIZATION TESTS
  // ============================================================================

  describe('Governance Rules Synchronization', () => {
    beforeEach(async () => {
      await bridge.initialize();
      await bridge.initializeBridge(testBridgeConfig);
    });

    test('should synchronize governance rules successfully', async () => {
      const rules = [testGovernanceRule];

      const result = await bridge.synchronizeGovernanceRules(rules);

      expect(result.success).toBe(true);
      expect(result.synchronizedRules).toBe(1);
      expect(result.errors).toHaveLength(0);
    });

    test('should handle empty rules array', async () => {
      const result = await bridge.synchronizeGovernanceRules([]);

      expect(result.success).toBe(true);
      expect(result.synchronizedRules).toBe(0);
      expect(result.errors).toHaveLength(0);
    });

    test('should handle invalid rules gracefully', async () => {
      const invalidRule = { ...testGovernanceRule, ruleId: '' };

      const result = await bridge.synchronizeGovernanceRules([invalidRule]);

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should handle partial synchronization failures', async () => {
      const validRule = testGovernanceRule;
      const invalidRule = { ...testGovernanceRule, ruleId: '', name: 'Invalid Rule' };

      const result = await bridge.synchronizeGovernanceRules([validRule, invalidRule]);

      expect(result.synchronizedRules).toBe(1);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  // ============================================================================
  // TRACKING DATA FORWARDING TESTS
  // ============================================================================

  describe('Tracking Data Forwarding', () => {
    beforeEach(async () => {
      await bridge.initialize();
      await bridge.initializeBridge(testBridgeConfig);
    });

    test('should forward tracking data successfully', async () => {
      const result = await bridge.forwardTrackingData(testTrackingData);

      expect(result.success).toBe(true);
      expect(result.forwardedData).toBe(1);
      expect(result.errors).toHaveLength(0);
    });

    test('should validate tracking data before forwarding', async () => {
      const invalidData = { ...testTrackingData, componentId: '' };

      const result = await bridge.forwardTrackingData(invalidData);

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should handle tracking data transformation', async () => {
      const result = await bridge.forwardTrackingData(testTrackingData);

      expect(result.success).toBe(true);
      expect(result.metadata).toBeDefined();
      expect(result.metadata.componentId).toBe(testTrackingData.componentId);
    });
  });

  // ============================================================================
  // CROSS-SYSTEM COMPLIANCE VALIDATION TESTS
  // ============================================================================

  describe('Cross-System Compliance Validation', () => {
    beforeEach(async () => {
      await bridge.initialize();
      await bridge.initializeBridge(testBridgeConfig);
    });

    test('should validate cross-system compliance successfully', async () => {
      const validationScope: TValidationScope = {
        systems: ['governance-system', 'tracking-system'],
        ruleTypes: ['compliance-check', 'data-governance'],
        timeRange: {
          startTime: new Date(Date.now() - 3600000), // 1 hour ago
          endTime: new Date()
        },
        includeHistorical: false,
        metadata: {}
      };

      const result = await bridge.validateCrossSystemCompliance(validationScope);

      expect(result.success).toBe(true);
      expect(result.complianceScore).toBeGreaterThan(0);
      expect(result.violations).toBeDefined();
    });

    test('should handle validation scope with no systems', async () => {
      const emptyScope: TValidationScope = {
        systems: [],
        ruleTypes: ['compliance-check'],
        timeRange: {
          startTime: new Date(Date.now() - 3600000),
          endTime: new Date()
        },
        includeHistorical: false,
        metadata: {}
      };

      const result = await bridge.validateCrossSystemCompliance(emptyScope);

      expect(result.success).toBe(true);
      expect(result.complianceScore).toBe(100); // No systems to validate
    });
  });

  // ============================================================================
  // EVENT HANDLING TESTS
  // ============================================================================

  describe('Event Handling', () => {
    beforeEach(async () => {
      await bridge.initialize();
      await bridge.initializeBridge(testBridgeConfig);
    });

    test('should handle governance events successfully', async () => {
      const governanceEvent: TGovernanceEvent = {
        eventId: 'gov-event-001',
        eventType: 'rule-change',
        sourceSystem: 'governance-system',
        timestamp: new Date(),
        data: { ruleId: testGovernanceRule.ruleId },
        metadata: {}
      };

      const result = await bridge.handleGovernanceEvent(governanceEvent);

      expect(result.success).toBe(true);
      expect(result.eventId).toBe(governanceEvent.eventId);
      expect(result.processingTime).toBeGreaterThan(0);
    });

    test('should handle tracking events successfully', async () => {
      const trackingEvent: TTrackingEvent = {
        eventId: 'track-event-001',
        eventType: 'data-update',
        sourceSystem: 'tracking-system',
        timestamp: new Date(),
        data: { componentId: testTrackingData.componentId },
        metadata: {}
      };

      const result = await bridge.handleTrackingEvent(trackingEvent);

      expect(result.success).toBe(true);
      expect(result.eventId).toBe(trackingEvent.eventId);
      expect(result.processingTime).toBeGreaterThan(0);
    });

    test('should handle invalid events gracefully', async () => {
      const invalidEvent = {
        eventId: '',
        eventType: 'invalid-type',
        sourceSystem: 'unknown-system',
        timestamp: new Date(),
        data: {},
        metadata: {}
      } as TGovernanceEvent;

      const result = await bridge.handleGovernanceEvent(invalidEvent);

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  // ============================================================================
  // HEALTH MONITORING TESTS
  // ============================================================================

  describe('Health Monitoring', () => {
    beforeEach(async () => {
      await bridge.initialize();
      await bridge.initializeBridge(testBridgeConfig);
    });

    test('should perform health check successfully', async () => {
      const healthStatus = await bridge.getBridgeHealthStatus();

      expect(healthStatus).toBeDefined();
      expect(healthStatus.overall).toBeDefined();
      expect(healthStatus.governanceSystem).toBeDefined();
      expect(healthStatus.trackingSystem).toBeDefined();
      expect(healthStatus.uptime).toBeGreaterThanOrEqual(0);
    });

    test('should get bridge metrics successfully', async () => {
      const metrics = await bridge.getBridgeMetrics();

      expect(metrics).toBeDefined();
      expect(metrics.uptime).toBeGreaterThanOrEqual(0);
      expect(metrics.memoryUsage).toBeGreaterThan(0);
      expect(metrics.totalOperations).toBeGreaterThanOrEqual(0);
    });

    test('should detect unhealthy systems', async () => {
      // Simulate unhealthy system by causing errors
      try {
        await bridge.forwardTrackingData({ ...testTrackingData, componentId: '' });
      } catch (error) {
        // Expected error
      }

      const healthStatus = await bridge.getBridgeHealthStatus();

      expect(healthStatus).toBeDefined();
      // Health status should reflect any issues
    });
  });

  // ============================================================================
  // DIAGNOSTICS TESTS
  // ============================================================================

  describe('Bridge Diagnostics', () => {
    beforeEach(async () => {
      await bridge.initialize();
      await bridge.initializeBridge(testBridgeConfig);
    });

    test('should perform comprehensive diagnostics', async () => {
      const diagnostics = await bridge.performBridgeDiagnostics();

      expect(diagnostics.success).toBe(true);
      expect(diagnostics.diagnosticsId).toBeDefined();
      expect(diagnostics.systemChecks).toBeDefined();
      expect(diagnostics.performanceMetrics).toBeDefined();
      expect(diagnostics.recommendations).toBeDefined();
    });

    test('should identify configuration issues', async () => {
      const diagnostics = await bridge.performBridgeDiagnostics();

      expect(diagnostics.systemChecks.configuration).toBeDefined();
      expect(diagnostics.systemChecks.connectivity).toBeDefined();
      expect(diagnostics.systemChecks.performance).toBeDefined();
    });

    test('should provide actionable recommendations', async () => {
      const diagnostics = await bridge.performBridgeDiagnostics();

      expect(diagnostics.recommendations).toBeInstanceOf(Array);
      expect(diagnostics.recommendations.length).toBeGreaterThan(0);
    });
  });

  // ============================================================================
  // PERFORMANCE VALIDATION TESTS
  // ============================================================================

  describe('Performance Validation', () => {
    beforeEach(async () => {
      await bridge.initialize();
      await bridge.initializeBridge(testBridgeConfig);
    });

    test('should meet <5ms operation requirements', async () => {
      const startTime = performance.now();
      await bridge.forwardTrackingData(testTrackingData);
      const endTime = performance.now();

      const executionTime = endTime - startTime;
      expect(executionTime).toBeLessThan(5); // <5ms requirement
    });

    test('should handle concurrent operations efficiently', async () => {
      const concurrentOperations = Array.from({ length: 5 }, (_, i) => {
        const data = { ...testTrackingData, componentId: `concurrent-${i}` };
        return bridge.forwardTrackingData(data);
      });

      const startTime = performance.now();
      const results = await Promise.all(concurrentOperations);
      const endTime = performance.now();

      const totalTime = endTime - startTime;
      const averageTime = totalTime / results.length;

      expect(averageTime).toBeLessThan(10); // Average should be reasonable
      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });

    test('should maintain performance under load', async () => {
      const loadTestOperations = [];

      for (let i = 0; i < 20; i++) {
        loadTestOperations.push(async () => {
          const data = { ...testTrackingData, componentId: `load-test-${i}` };
          return await bridge.forwardTrackingData(data);
        });
      }

      const startTime = performance.now();
      const results = await Promise.all(loadTestOperations.map(op => op()));
      const endTime = performance.now();

      const totalTime = endTime - startTime;
      const averageTime = totalTime / results.length;

      expect(averageTime).toBeLessThan(15); // Should maintain reasonable performance
      expect(results.every(r => r.success)).toBe(true);
    });
  });

  // ============================================================================
  // RESILIENT TIMING INTEGRATION TESTS
  // ============================================================================

  describe('Resilient Timing Integration', () => {
    beforeEach(async () => {
      await bridge.initialize();
      await bridge.initializeBridge(testBridgeConfig);
    });

    test('should use resilient timing for all operations', async () => {
      const { createResilientTimer } = require('../../../../../../shared/src/base/timer-coordination/modules/TimerConfiguration');

      await bridge.forwardTrackingData(testTrackingData);

      // Verify resilient timer was used
      expect(createResilientTimer).toHaveBeenCalled();
    });

    test('should collect performance metrics', async () => {
      const { createResilientMetricsCollector } = require('../../../../../../shared/src/base/timer-coordination/modules/TimerConfiguration');

      await bridge.forwardTrackingData(testTrackingData);

      // Verify metrics collector was used
      expect(createResilientMetricsCollector).toHaveBeenCalled();
    });

    test('should handle timing failures gracefully', async () => {
      // Mock timing failure
      const { createResilientTimer } = require('../../../../../../shared/src/base/timer-coordination/modules/TimerConfiguration');
      createResilientTimer.mockImplementationOnce(() => {
        throw new Error('Timing system failure');
      });

      // Should still complete operation
      await expect(bridge.forwardTrackingData(testTrackingData)).resolves.toBeDefined();
    });
  });
});
